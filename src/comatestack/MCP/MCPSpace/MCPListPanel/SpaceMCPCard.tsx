import {Flex, Divider, Typography} from 'antd';
import {memo, useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {MCPEditLink} from '@/links/mcp';
import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPDetailButton} from '@/components/MCP/MCPDetailButton';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MC<PERSON><PERSON> from '@/design/MCP/MCPCard';
import MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';
import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import UpdateInfo from '@/components/MCP/UpdateInfo';

const containerCss = css`
    padding: 0 20px 12px;
`;
interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const TooltipContainer = styled.div`
    max-height: 200px;
    word-wrap: break-word;
    overflow: auto;
`;

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const tags = useMemo(
        () => (server.labels ?? []).map(label => label.labelValue),
        [server.labels]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <Flex align="flex-end" gap={14} style={{overflow: 'hidden', marginRight: '-20px'}}>
                <MCPServerAvatar icon={server.icon} style={{marginBottom: 4}} />
                <Flex vertical style={{overflow: 'hidden', flex: 1}}>
                    <Flex align="flex-start" justify="space-between" gap={8}>
                        <Typography.Title level={4} style={{margin: '16px 0 4px'}} ellipsis>
                            {server.name}
                        </Typography.Title>
                        <MCPReleaseStatus
                            status={server.serverStatus}
                            publishType={server.serverPublishType}
                            style={{flexShrink: 0}}
                        />
                    </Flex>
                    <Typography.Text
                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
                    >
                        {server.departmentName || '暂无部门信息'}
                    </Typography.Text>
                </Flex>
            </Flex>
            <Typography.Paragraph
                type="secondary"
                ellipsis={{tooltip: <TooltipContainer>{server.description}</TooltipContainer>, rows: 2}}
                style={{margin: '16px 0 12px', height: 44}}
            >
                {server.description || '暂无描述'}
            </Typography.Paragraph>
            <Flex gap={4}>
                <MCPServerTypeTag type={server.serverSourceType} />
                <MCPServerProtocolTypeTag type={server.serverProtocolType} />
                <TagGroup
                    labels={tags.map((label, index) => ({id: index, label}))}
                    color="light-purple"
                    prefix={null}
                    style={{flexShrink: 1, overflow: 'hidden'}}
                    gap={4}
                />
            </Flex>
            <Divider style={{margin: '16px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />
            <Flex justify="space-between" align="center" gap={4}>
                <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} />
                <Flex align="center" gap={8}>
                    <MCPDetailButton serverId={server.id} />
                    <Divider type="vertical" style={{height: 16, margin: 0}} />
                    <MCPSubscribeButton
                        refresh={refresh}
                        id={server.id}
                        workspaceId={spaceId}
                        showText
                        showCount
                        size="small"
                    />
                </Flex>
            </Flex>
        </MCPCard>
    );
};

export default memo(SpaceMCPCard);
