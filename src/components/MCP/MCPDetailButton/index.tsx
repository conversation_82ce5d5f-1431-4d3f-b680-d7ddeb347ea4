import {FileSearchOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {useCallback, MouseEvent} from 'react';

interface Props {
    serverId: number;
}

export const MCPDetailButton = ({serverId}: Props) => {
    const handleClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = `http://console.cloud-sandbox.baidu-int.com/comatestack/mcp/square/server/${serverId}`;
            window.open(url);
        },
        [serverId]
    );

    return (
        <Button
            icon={<FileSearchOutlined />}
            onClick={handleClick}
            style={{padding: 0}}
        >
            详情
        </Button>
    );
};
