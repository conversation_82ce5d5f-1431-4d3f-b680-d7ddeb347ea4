import {FileSearchOutlined} from '@ant-design/icons';
import {Button, ButtonProps} from '@panda-design/components';
import {useCallback, MouseEvent, CSSProperties} from 'react';

interface Props {
    serverId: number;
    size?: ButtonProps['size'];
    style?: CSSProperties;
}

export const MCPDetailButton = ({serverId, size, style}: Props) => {
    const handleClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = `http://console.cloud-sandbox.baidu-int.com/comatestack/mcp/square/server/${serverId}`;
            window.open(url, '_blank');
        },
        [serverId]
    );

    return (
        <Button
            style={style}
            onClick={handleClick}
            icon={<FileSearchOutlined />}
            type="text"
            size={size}
        >
            详情
        </Button>
    );
};
